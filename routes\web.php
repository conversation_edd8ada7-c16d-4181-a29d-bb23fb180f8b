<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Guest\MainController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Guest\CommonController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\User\UserController;


Route::get('/cache', function () {
    Artisan::call('optimize:clear');
    return redirect()->route('login')->with('success', 'All caches cleared successfully!');
});

Route::post('logout', [AuthController::class, 'logout'])->name('logout');

// Public routes - admins will be redirected to dashboard
Route::middleware(['admin.redirect'])->group(function () {
    Route::get('/', [MainController::class, 'index'])->name('home');
    Route::get('/about', [MainController::class, 'about'])->name('about');
    Route::get('/terms', [MainController::class, 'terms'])->name('terms');
    Route::get('/privacy', [MainController::class, 'privacy'])->name('privacy');
    Route::get('/pricing', [MainController::class, 'pricing'])->name('pricing');
    Route::get('/contact', [MainController::class, 'contact'])->name('contact');

    // Contact form submission
    Route::post('/contact/submit', [CommonController::class, 'submitContact'])->name('contact.submit');
});

// Auth Routes - only accessible to guests (non-authenticated users)
Route::middleware(['guest.only'])->group(function () {
    Route::get('login', [AuthController::class, 'index'])->name('login');
    Route::get('register', [AuthController::class, 'register'])->name('register');
    Route::post('login-post', [AuthController::class, 'login'])->name('login.post');
    Route::post('register-post', [AuthController::class, 'registerPost'])->name('register.post');
});

Route::middleware(['auth', 'admin.role'])->prefix('admin')->group(function () {
    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('profile', [HomeController::class, 'showProfile'])->name('profile.show');
    Route::put('profile-update', [HomeController::class, 'updateProfile'])->name('profile.update');

    Route::resource('plan', PlanController::class);
    Route::post('plan/{id}/status', [PlanController::class, 'status'])->name('plan.status');
    Route::post('plan/{id}/popular', [PlanController::class, 'popular'])->name('plan.popular');

    // Contact management routes
    Route::resource('contact', ContactController::class)->only(['index', 'show', 'destroy']);
    Route::post('contact/{id}/status', [ContactController::class, 'status'])->name('contact.status');
});

Route::middleware(['auth', 'user.role'])->prefix('user')->group(function () {
    Route::get('dashboard', [UserController::class, 'dashboard'])->name('user.dashboard');
    Route::get('profile', [UserController::class, 'profile'])->name('user.profile');
    Route::put('profile', [UserController::class, 'updateProfile'])->name('user.profile.update');
    Route::get('bookings', [UserController::class, 'bookings'])->name('user.bookings');
});
